<!--
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-26 14:43:16
 * @LastEditTime: 2025-08-26 14:48:50
 * @FilePath: /day3/todo.html
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
<script src="https://cdn.staticfile.net/vue/3.5.13/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <todo />
    </div>
    <script>
        const todo={
            data(){
                return {title:'todo edit'}
            },
            template:`<div>{{title}}</div>`
        }
        const app = Vue.createApp({
            components:{
                todo
            }
        })
        app.mount('#app')
    </script>
</body>
</html>