<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
<script src="https://cdn.bootcdn.net/ajax/libs/vue/3.5.18/vue.global.js"></script>
</head>
<body>
    <div id="app"></div> <!-- 这里不再直接包含内容，由app的template控制 -->

    <!-- 定义 msTemplate -->
    <script type="text/x-template" id="msTemplate">
        <div>
            <h1>我是ms-component组件-<vue-nb></vue-nb></h1>
        </div>
    </script>

    <script>
        // 检查Vue是否加载成功
        console.log('Vue对象:', Vue);

        const { createApp } = Vue;

        const app = createApp({
            template: `<div>迷失-<ms-component /></div>`
        });

        // 先注册子组件 vue-nb
        app.component('vue-nb', {
            template: `<div>我是vuenb组件</div>`
        });

        // 再注册父组件 ms-component，因为它依赖 vue-nb 组件
        app.component('ms-component', {
            template: '#msTemplate'
        });

        // 挂载应用到#app元素
        console.log('准备挂载应用...');
        app.mount('#app');
        console.log('应用已挂载');
    </script>
</body>
</html>
